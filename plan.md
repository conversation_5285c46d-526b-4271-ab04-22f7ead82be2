# 为期一个月的"AI策略产品工程师"冲刺计划

## 第一周：理论巩固与认知对齐 (1-7天)

**目标：** 快速将你的知识体系从3D视觉对齐到大模型应用层，并建立对AI产品和相关业务的宏观认知。

### 1. 核心技术专项学习 (每天2小时)

**主题：** 深入理解 RAG (检索增强生成) 和 Agent 的工作原理。这几乎是所有应用层岗位的必考题。

**行动：**
- 精读至少2篇关于RAG的经典论文或技术博客，理解其相比于微调(Fine-tuning)的优势和应用场景
- 研究1-2个主流的Agent框架（如ReAct, AutoGPT），画出它们的工作流程图，理解"思考-行动-观察"的循环

**产出：** 一份你自己的学习笔记，能清晰地向面试官解释什么是RAG和Agent，以及它们的区别。

### 2. AI产品"品鉴" (每天1小时)

**主题：** 成为AI产品的重度用户，并从"产品经理"的视角进行解构。这直接对应JD中的"高频使用各类AI产品"的要求。

**行动：**
- 深度使用3款以上的AI产品，建议组合：
  - Perplexity.ai (看RAG在搜索中的应用)
  - Character.ai (看多智能体和角色扮演)
  - Kimi智能助手 (看国产大模型的长文本处理能力)
- 为其中至少一款产品撰写一份简短的分析报告，包含：核心用户、解决的核心痛点、产品亮点与槽点、可能的商业模式

**产出：** 一份产品分析报告，以及你在面试中可以随时引用的产品案例。

### 3. 业务知识输入 (每天30分钟)

**主题：** 快速了解大模型在"搜索"和"内容生态"中的应用。

**行动：** 阅读行业报告、科技媒体上关于"AI+搜索"、"AIGC+内容创作"的文章，了解主流公司的做法和未来的趋势。

**产出：** 对业务有初步的理解，能和面试官聊几句行业动态。

---

## 第二周：动手实践与原型构建 (8-14天)

**目标：** 完成一个能写在简历上的大模型应用Demo。这是本月计划的核心，直接满足"输出产品Demo或搭建AI应用等工作流"的要求。

### 1. 确定并设计你的Mini-Project (第8-9天)

**主题：** 从小切口入手，做一个能体现RAG思想的简单应用。

**行动：** 从以下几个方向中选择一个：
- **PDF文档问答助手：** 上传一份你熟悉的领域（比如NeRF）的PDF论文，通过提问让AI回答论文中的细节问题
- **个人知识库检索引擎：** 将你过去的一些笔记、文章（Markdown格式）作为知识库，构建一个能回答你个人知识的AI
- **视频内容摘要与问答：** 使用工具将一段视频转为文本，构建一个能对视频内容进行总结和问答的应用

### 2. 技术实现 (第10-14天)

**主题：** 使用主流框架快速实现MVP（最小可行产品）。

**行动：**
- **框架：** 建议使用 LangChain 或 LlamaIndex 库，它们封装好了RAG的流程，能让你快速上手
- **模型API：** 调用任意一个大模型API即可
- **前端：** 使用 Streamlit 或 Gradio，几行代码就能生成一个可以交互的Web界面

**产出：** 一个可以运行、可以展示的个人项目URL（可以部署在Hugging Face Spaces或云服务器上），以及完整的项目代码。

---

## 第三周：案例沉淀与简历迭代 (15-21天)

**目标：** 将第二周的实践成果，转化为求职中的"弹药"，并全面升级你的简历。

### 1. 项目复盘与故事线梳理 (第15-16天)

**主题：** 把你的项目变成一个精彩的STAR故事（Situation, Task, Action, Result）。

**行动：** 撰写一份详细的项目文档，说明：
- **S (背景)：** 你想解决什么问题？
- **T (任务)：** 你的目标是什么？（例如：实现一个能准确回答特定PDF内容的AI助手）
- **A (行动)：** 你是如何做的？（例如：我使用了LangChain框架，结合...模型API，通过...方式处理文本，并用Streamlit搭建了前端界面）
- **R (结果)：** 达到了什么效果？（例如：实现了对XX文档的精准问答，回答准确率约XX%）

**产出：** 一段可以写在简历上、可以在面试中清晰讲述的项目描述。

### 2. 简历"魔改" (第17-18天)

**主题：** 针对目标岗位 (A153211, A80240A) 的JD，逐字逐句地优化你的简历。

**行动：**
- 将你的新项目作为"核心项目经历"放在最显眼的位置
- 将JD中的关键词，如"大模型应用"、"AI应用工作流"、"策略设计"、"Agent"、"RAG"，有机地融入到你的项目描述和技能列表中
- 重新包装你的硕士研究，强调其在"复杂AI模型研究与实现"上的共通能力

**产出：** 一份针对性极强的全新简历。

### 3. 面试"弹药库"准备 (第19-21天)

**主题：** 准备面试中关于项目、大模型、过往经历的高频问题。

**行动：** 针对你的简历，预测可能被问到的问题并写下回答要点。例如：
- "详细介绍一下你的XX大模型项目。"
- "你认为RAG最大的挑战是什么？"
- "你在TikTok的PMO经历，对你做产品有什么帮助？"

**产出：** 一份包含20个以上核心问题的回答要点文档。

---

## 第四周：模拟实战与深度思考 (22-30天)

**目标：** 模拟真实面试环境，锻炼表达能力和临场反应，并拔高你的行业思考深度。

### 1. 产品设计模拟 (2-3次)

**主题：** 针对抖音的业务场景，进行模拟产品设计。

**行动：** 自己给自己出题，并进行完整解答。例如：
- "请为抖音设计一个AI功能，帮助创作者寻找视频灵感。"
- "如果要用大模型优化抖音的搜索结果，你会从哪几个方面入手？"

按照"问题发现 → 用户分析 → 方案设计 → 核心指标 → 风险评估"的框架进行回答。

**产出：** 几个可以随时在面试中作为案例抛出的产品设计方案。

### 2. 真人模拟面试 (至少2次)

**主题：** 寻找有经验的同学、朋友或使用平台服务，进行正式的模拟面试。

**行动：** 将你准备好的简历和"弹药库"拿出来实战，重点考察：
- 自我介绍是否流畅且突出重点
- 项目介绍是否清晰，能否应对追问
- 行为面试题（BQ，如跨团队协作）能否结合你的字节经历讲出细节

**产出：** 发现自己的盲点和表达问题，并在下一次模拟中改进。

### 3. 形成个人观点 (持续进行)

**主题：** 对AI行业形成自己独到的见解。

**行动：** 思考一些开放性问题，例如：
- "你认为当前大模型应用最大的泡沫是什么？"
- "未来一年，你最看好哪个AIGC方向的突破？"

**产出：** 在面试的最后"反问环节"，可以提出有深度的问题，展现你的思考能力。

---

## 总结

这个一个月的冲刺计划旨在帮助你从3D视觉背景快速转向AI策略产品工程师角色。通过理论学习、实践项目、简历优化和面试准备四个阶段，系统性地建立相关技能和经验。

**关键成功因素：**
- 保持每日学习的节奏和强度
- 重视实践项目的质量和完整性
- 针对性地准备面试材料
- 持续关注行业动态和技术发展

祝你求职成功！🚀
